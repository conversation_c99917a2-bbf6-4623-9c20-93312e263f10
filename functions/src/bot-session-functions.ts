import * as admin from "firebase-admin";
import { HttpsError, onCall } from "firebase-functions/v2/https";
import { verifyBotToken } from "./services/bot-auth-service";
import { BotSessionEntity } from "./types";
import { CORS_CONFIG } from "./config";
import { log } from "./utils/logger";

const db = admin.firestore();

export const saveUserSessionByBot = onCall<{
  userId: string;
  botToken: string;
  sessionData: {
    pendingOrderId?: string;
    echoMode?: boolean;
  };
}>({ cors: CORS_CONFIG }, async (request) => {
  const { userId, botToken, sessionData } = request.data;

  if (!userId) {
    throw new HttpsError("invalid-argument", "User ID is required.");
  }

  if (!botToken) {
    throw new HttpsError("invalid-argument", "Bot token is required.");
  }

  if (!verifyBotToken(botToken)) {
    throw new HttpsError("permission-denied", "Invalid bot token.");
  }

  try {
    const sessionDoc = db.collection("bot_sessions").doc(userId);
    const existingSession = await sessionDoc.get();

    const sessionEntity: BotSessionEntity = {
      id: userId,
      pendingOrderId: sessionData.pendingOrderId,
      echoMode: sessionData.echoMode,
      createdAt: existingSession.exists
        ? existingSession.data()?.createdAt
        : admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
    };

    await sessionDoc.set(sessionEntity, { merge: true });

    log.info("Bot session saved successfully", {
      operation: "save_bot_session",
      userId,
      sessionData,
    });

    return {
      success: true,
      message: "Session saved successfully",
    };
  } catch (error) {
    log.error("Error saving bot session", error, {
      operation: "save_bot_session",
      userId,
    });
    throw new HttpsError("internal", "Failed to save session");
  }
});

export const getUserSessionByBot = onCall<{
  userId: string;
  botToken: string;
}>({ cors: CORS_CONFIG }, async (request) => {
  const { userId, botToken } = request.data;

  if (!userId) {
    throw new HttpsError("invalid-argument", "User ID is required.");
  }

  if (!botToken) {
    throw new HttpsError("invalid-argument", "Bot token is required.");
  }

  if (!verifyBotToken(botToken)) {
    throw new HttpsError("permission-denied", "Invalid bot token.");
  }

  try {
    const sessionDoc = await db.collection("bot_sessions").doc(userId).get();

    if (!sessionDoc.exists) {
      log.info("Bot session not found", {
        operation: "get_bot_session",
        userId,
      });
      return {
        success: true,
        session: null,
      };
    }

    const sessionData = sessionDoc.data() as BotSessionEntity;

    log.info("Bot session retrieved successfully", {
      operation: "get_bot_session",
      userId,
    });

    return {
      success: true,
      session: {
        pendingOrderId: sessionData.pendingOrderId,
        echoMode: sessionData.echoMode,
      },
    };
  } catch (error) {
    log.error("Error retrieving bot session", error, {
      operation: "get_bot_session",
      userId,
    });
    throw new HttpsError("internal", "Failed to retrieve session");
  }
});
