import { log } from "../utils/logger";

export class HealthcheckService {
  private static botStartTime: Date = new Date();
  private static healthcheckInterval: NodeJS.Timeout | null = null;

  static getBotStartTime(): Date {
    return this.botStartTime;
  }

  static setBotStartTime(): void {
    this.botStartTime = new Date();
    log.healthLog("Bot start time updated", {
      status: "bot_start_time_set",
      timestamp: this.botStartTime.toISOString(),
    });
  }

  static startPeriodicHealthcheck(): void {
    // Periodic healthcheck functionality removed - health is now based on bot uptime
    log.healthLog(
      "Periodic healthcheck disabled - using bot uptime for health status",
      {
        status: "periodic_healthcheck_disabled",
      }
    );
  }

  static stopPeriodicHealthcheck(): void {
    if (this.healthcheckInterval) {
      clearInterval(this.healthcheckInterval);
      this.healthcheckInterval = null;
      log.healthLog("Periodic healthcheck stopped", {
        status: "periodic_healthcheck_stopped",
      });
    }
  }

  static async getLastHealthcheck(): Promise<string | null> {
    // Return bot start time as the last healthcheck
    return this.botStartTime.toISOString();
  }

  static async isHealthy(): Promise<boolean> {
    try {
      const now = new Date();
      const timeDiff = now.getTime() - this.botStartTime.getTime();
      const hoursDiff = timeDiff / (1000 * 60 * 60);

      // Consider healthy if bot has been running for less than 24 hours without restart
      // This is a simple health check based on uptime
      return hoursDiff < 24;
    } catch (error) {
      log.error("Failed to check health status", error, {
        operation: "healthcheck_status",
      });
      return false;
    }
  }
}
