import axios from "axios";
import { UserSession } from "../types/session";
import { log } from "../utils/logger";
import { loadEnvironment } from "../config/env-loader";

loadEnvironment();

const FIREBASE_PROJECT_ID = process.env.FIREBASE_PROJECT_ID;
const FIREBASE_REGION = process.env.FIREBASE_REGION ?? "us-central1";
const BOT_TOKEN = process.env.BOT_TOKEN;

if (!FIREBASE_PROJECT_ID) {
  throw new Error("FIREBASE_PROJECT_ID is required in environment variables");
}

if (!BOT_TOKEN) {
  throw new Error("BOT_TOKEN is required in environment variables");
}

const FIREBASE_FUNCTIONS_BASE_URL = `https://${FIREBASE_REGION}-${FIREBASE_PROJECT_ID}.cloudfunctions.net`;

const getFunctionUrl = (functionName: string): string => {
  return `${FIREBASE_FUNCTIONS_BASE_URL}/${functionName}`;
};

export const setUserSession = async (
  userId: string,
  session: UserSession
): Promise<void> => {
  try {
    const response = await axios.post(
      getFunctionUrl("saveUserSessionByBot"),
      {
        data: {
          userId,
          botToken: BOT_TOKEN,
          sessionData: session,
        },
      },
      {
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

    if (!response.data.result.success) {
      throw new Error(response.data.result.message ?? "Failed to save session");
    }
  } catch (error) {
    log.error(`Failed to set session for user ${userId}`, error, {
      operation: "set_user_session",
      userId,
    });
    throw error;
  }
};

export const getUserSession = async (
  userId: string
): Promise<UserSession | undefined> => {
  try {
    const response = await axios.post(
      getFunctionUrl("getUserSessionByBot"),
      {
        data: {
          userId,
          botToken: BOT_TOKEN,
        },
      },
      {
        headers: {
          "Content-Type": "application/json",
        },
      }
    );

    if (!response.data.result.success) {
      throw new Error(response.data.result.message ?? "Failed to get session");
    }

    return response.data.result.session ?? undefined;
  } catch (error) {
    log.error(`Failed to get session for user ${userId}`, error, {
      operation: "get_user_session",
      userId,
    });
    return undefined;
  }
};

export const clearUserSession = async (userId: string): Promise<void> => {
  try {
    await axios.post(
      getFunctionUrl("saveUserSessionByBot"),
      {
        data: {
          userId,
          botToken: BOT_TOKEN,
          sessionData: {}, // Empty session data to clear
        },
      },
      {
        headers: {
          "Content-Type": "application/json",
        },
      }
    );
  } catch (error) {
    log.error(`Failed to clear session for user ${userId}`, error, {
      operation: "clear_user_session",
      userId,
    });
    throw error;
  }
};

export const updateUserSession = async (
  userId: string,
  updates: Partial<UserSession>
): Promise<void> => {
  try {
    const currentSession = (await getUserSession(userId)) ?? {};
    const updatedSession = { ...currentSession, ...updates };
    await setUserSession(userId, updatedSession);
  } catch (error) {
    log.error(`Failed to update session for user ${userId}`, error, {
      operation: "update_user_session",
      userId,
    });
    throw error;
  }
};

export const clearSessionProperty = async (
  userId: string,
  property: keyof UserSession
): Promise<void> => {
  try {
    const session = await getUserSession(userId);
    if (session) {
      delete session[property];
      if (Object.keys(session).length === 0) {
        await clearUserSession(userId);
      } else {
        await setUserSession(userId, session);
      }
    }
  } catch (error) {
    log.error(
      `Failed to clear session property ${property} for user ${userId}`,
      error,
      {
        operation: "clear_session_property",
        userId,
        property,
      }
    );
    throw error;
  }
};
